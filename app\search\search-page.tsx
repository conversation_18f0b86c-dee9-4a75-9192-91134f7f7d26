'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Search, MapPin, Filter, X, Menu, ChevronDown, Clock, Star, AlertTriangle } from 'lucide-react'
import { useLocation } from '@/context/location-context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import SearchFilters from '@/components/filters/search-filters'
import MobileSearchPage from '@/components/mobile-search/mobile-search-page'
import UnifiedBusinessCard from '@/components/unified-business-card'

import CategoriesAndFilters from '@/components/search/categories-and-filters'
import {
  getAllRestaurants,
  getAllShops,
  getAllBusinessesByType
} from '@/services/business-service-direct'
import { getBusinessTypeColors } from '@/utils/business-colors'
import { useAttributeFilters } from '@/hooks/use-attribute-filters'
import { parseAttributeFilterId } from '@/services/attribute-service-direct'
import type { Restaurant, Shop } from '@/types/business'
import { isValidJerseyPostcodeFormat, standardizeJerseyPostcodeFormat } from '@/lib/jersey-postcodes'

export default function SearchPage() {
  console.log('🔍 Search Page: Component rendering')

  const searchParams = useSearchParams()
  const initialQuery = searchParams?.get('q') || ''
  const initialType = searchParams?.get('type') || 'all'
  const initialPostcodeParam = searchParams?.get('postcode') || ''

  console.log('🔍 Search Page: Initial values from URL:', { initialQuery, initialType, initialPostcodeParam })

  // Use the location context to access stored postcode and coordinates
  const {
    postcode: contextPostcode,
    setPostcode: setContextPostcode,
    coordinates,
    geocodePostcode,
    userId
  } = useLocation()

  const [searchQuery, setSearchQuery] = useState(initialQuery)
  const [activeType, setActiveType] = useState(initialType)

  // Debug log for location context values
  console.log('🔍 Search Page: Location context values:', {
    contextPostcode,
    coordinates,
    userId,
    activeType
  })
  const [businesses, setBusinesses] = useState<(Restaurant | Shop)[]>([])
  const [filteredBusinesses, setFilteredBusinesses] = useState<(Restaurant | Shop)[]>([])
  const [loading, setLoading] = useState(true)
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 50])
  const [maxDeliveryTime, setMaxDeliveryTime] = useState<number>(30)
  const [maxPrepTime, setMaxPrepTime] = useState<number>(30)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])

  // Debug log for initial filter values
  console.log('🔍 Search Page: Initial filter values - priceRange:', priceRange, 'maxDeliveryTime:', maxDeliveryTime, 'maxPrepTime:', maxPrepTime)
  console.log('🔍 Search Page: Initial values - activeType:', activeType, 'contextPostcode:', contextPostcode, 'userId:', userId)
  const [isMobile, setIsMobile] = useState<boolean>(false)

  // State for postcode dialog
  const [postcodeDialogOpen, setPostcodeDialogOpen] = useState(false);
  const [newPostcode, setNewPostcode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Listen for URL parameter changes and update state accordingly
  useEffect(() => {
    const currentQuery = searchParams?.get('q') || ''
    const currentType = searchParams?.get('type') || 'all'

    console.log('🔍 Search Page: URL params changed:', { currentQuery, currentType })

    // Update state if URL parameters have changed
    if (currentQuery !== searchQuery) {
      console.log('🔍 Search Page: Updating search query from URL:', currentQuery)
      setSearchQuery(currentQuery)
    }

    if (currentType !== activeType) {
      console.log('🔍 Search Page: Updating active type from URL:', currentType)
      setActiveType(currentType)
    }
  }, [searchParams, searchQuery, activeType])

  // Function to validate Jersey postcode format
  const isValidJerseyPostcode = (postcode: string): boolean => {
    // Use our new standardized function
    return isValidJerseyPostcodeFormat(postcode);
  };

  // Format a Jersey postcode to ensure it has a space
  const formatJerseyPostcode = (postcode: string): string => {
    // Use our new standardized function
    const standardized = standardizeJerseyPostcodeFormat(postcode);
    return standardized || postcode.trim().toUpperCase();
  };

  // Handle proceeding with default coordinates
  const handleProceedWithDefault = () => {
    setIsSubmitting(true);
    try {
      // Use the invalid postcode but let the system use default coordinates
      const trimmedPostcode = newPostcode.trim();

      // Update the context with the invalid postcode
      // The location context will handle using default coordinates
      setContextPostcode(trimmedPostcode);

      // Try to geocode the postcode (will use default coordinates)
      geocodePostcode(trimmedPostcode);

      // Close the dialog
      setPostcodeDialogOpen(false);

      // Reset the form
      setNewPostcode('');
      setValidationError(null);

      // Update URL with the postcode without page reload
      const url = new URL(window.location.href);
      url.searchParams.set('postcode', trimmedPostcode);
      window.history.pushState({}, '', url.toString());

      // Force a reload of the businesses to recalculate delivery times
      setFilteredBusinesses([...filteredBusinesses]);
    } catch (error) {
      // Error handling for proceeding with default location
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle postcode submission
  const handlePostcodeSubmit = async () => {
    if (!newPostcode.trim()) return;

    setValidationError(null);
    setIsSubmitting(true);

    try {
      // Validate the postcode format
      const trimmedPostcode = newPostcode.trim();

      if (!isValidJerseyPostcode(trimmedPostcode)) {
        setValidationError(`"${trimmedPostcode}" is not a valid Jersey postcode. Jersey postcodes should be in the format JE1 1AA, JE2 3BT, etc. Default Jersey coordinates will be used to calculate delivery times and fee.`);
        setIsSubmitting(false);
        return;
      }

      // Format the postcode properly
      const formattedPostcode = formatJerseyPostcode(trimmedPostcode);

      // Save to session and update context
      const response = await fetch('/api/user/location', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ postcode: formattedPostcode }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save location');
      }

      // Update the context with the new postcode
      setContextPostcode(formattedPostcode);

      // Close the dialog
      setPostcodeDialogOpen(false);

      // Reset the form
      setNewPostcode('');
      setValidationError(null);

      // Update URL with the new postcode without page reload
      const url = new URL(window.location.href);
      url.searchParams.set('postcode', formattedPostcode);
      window.history.pushState({}, '', url.toString());

      // Force a reload of the businesses to recalculate delivery times
      setFilteredBusinesses([...filteredBusinesses]);
    } catch (error) {
      setValidationError('Failed to save your location. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Sync postcode from URL parameters and try to geocode it if needed
  useEffect(() => {
    const syncPostcodeFromUrlOrSession = async () => {
      try {
        // First check if we have a postcode in the URL
        if (initialPostcodeParam && initialPostcodeParam !== contextPostcode) {
          // Validate the postcode format
          if (isValidJerseyPostcode(initialPostcodeParam)) {
            // Format the postcode properly
            const formattedPostcode = formatJerseyPostcode(initialPostcodeParam);

            // Save to session and update context
            await fetch('/api/user/location', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ postcode: formattedPostcode }),
            });

            setContextPostcode(formattedPostcode);

            // If we don't have coordinates for this postcode, try to geocode it
            if (!coordinates) {
              geocodePostcode(formattedPostcode).catch(() => {
                // Error handling for geocoding
              });
            }

            // Force a reload of the businesses to recalculate delivery times
            setFilteredBusinesses([...filteredBusinesses]);
          } else {
            // Show the postcode dialog with validation error
            setNewPostcode(initialPostcodeParam);
            setValidationError(`"${initialPostcodeParam}" is not a valid Jersey postcode. Jersey postcodes should be in the format JE1 1AA, JE2 3BT, etc. Default Jersey coordinates will be used to calculate delivery times and fee.`);
            setPostcodeDialogOpen(true);
          }
        }
        // If no postcode in URL, check session
        else if (!contextPostcode) {
          const response = await fetch('/api/user/location');
          const data = await response.json();

          if (data.location?.postcode) {
            setContextPostcode(data.location.postcode);

            // If we don't have coordinates, try to geocode it
            if (!coordinates && data.location.coordinates) {
              // Here we would update the coordinates in the location context
              // This depends on how your location context is implemented
            } else if (!coordinates) {
              geocodePostcode(data.location.postcode).catch(() => {
                // Error handling for geocoding
              });
            }
          } else {
            setPostcodeDialogOpen(true);
          }
        }
      } catch (error) {
        // If all else fails, show the postcode dialog
        setPostcodeDialogOpen(true);
      }
    };

    syncPostcodeFromUrlOrSession();
  }, [initialPostcodeParam, contextPostcode, coordinates, geocodePostcode, setContextPostcode, filteredBusinesses]);

  // Check if we're on mobile/tablet
  useEffect(() => {
    const checkIfMobile = () => {
      const width = window.innerWidth
      const isMobileView = width < 1024
      setIsMobile(isMobileView)
    }

    // Initial check
    checkIfMobile()

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile)

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Function to get placeholder text based on business type
  const getPlaceholderText = (type: string) => {
    switch (type) {
      case 'restaurant':
        return 'Search by name, location, or cuisine...'
      case 'shop':
        return 'Search by name, location, or product...'
      case 'pharmacy':
        return 'Search by name, location, or product...'
      case 'cafe':
        return 'Search by name, location, or menu item...'
      case 'errand':
        return 'Search by name, location, or service type...'
      default:
        return 'Search by name, location, or business type...'
    }
  }

  // Load businesses based on active type
  useEffect(() => {
    const loadBusinesses = async () => {
      console.log('🔍 Search Page: Loading businesses for type:', activeType)
      setLoading(true)

      try {
        let businessData: any[] = []

        switch (activeType) {
          case 'restaurant':
            businessData = await getAllRestaurants(contextPostcode)
            break
          case 'shop':
            businessData = await getAllShops(contextPostcode)
            break
          case 'pharmacy':
            businessData = await getAllBusinessesByType('pharmacy', contextPostcode)
            break
          case 'cafe':
            businessData = await getAllBusinessesByType('cafe', contextPostcode)
            break
          case 'errand':
            businessData = await getAllBusinessesByType('errand', contextPostcode)
            break
          case 'all':
          default:
            // Load all business types
            const [restaurants, shops, pharmacies, cafes, errands] = await Promise.all([
              getAllRestaurants(contextPostcode),
              getAllShops(contextPostcode),
              getAllBusinessesByType('pharmacy', contextPostcode),
              getAllBusinessesByType('cafe', contextPostcode),
              getAllBusinessesByType('errand', contextPostcode)
            ])
            businessData = [...restaurants, ...shops, ...pharmacies, ...cafes, ...errands]
            break
        }

        console.log('🔍 Search Page: Loaded businesses:', businessData.length)
        console.log('🔍 Search Page: First business:', businessData[0])
        setBusinesses(businessData)
      } catch (error) {
        console.error('🔍 Search Page: Error loading businesses:', error)
        setBusinesses([])
      } finally {
        setLoading(false)
      }
    }

    loadBusinesses()
  }, [activeType, contextPostcode])

  // Refresh businesses when postcode changes to update delivery times
  useEffect(() => {
    if (businesses.length > 0 && contextPostcode) {
      // Force a re-render of the businesses to recalculate delivery times
      // We're using a timeout to ensure this doesn't cause render issues
      const timer = setTimeout(() => {
        // Create a fresh copy of the businesses array to trigger recalculation
        setFilteredBusinesses(businesses.slice());
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [contextPostcode]);

  // Filter businesses based on search query and categories
  useEffect(() => {
    const applyFilters = async () => {
      console.log('🔍 Search Page: Filtering businesses. Total:', businesses.length, 'Search query:', searchQuery, 'Active filters:', activeFilters.length, 'Selected categories:', selectedCategories.length)

      if (!businesses.length) {
        console.log('🔍 Search Page: No businesses to filter')
        setFilteredBusinesses([])
        return
      }

      // First filter by search query
      let filtered = businesses.filter(business => {
        const matchesSearch =
          searchQuery === '' ||
          business.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          business.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
          hasMatchingAttribute(business, searchQuery)

        return matchesSearch
      })

      console.log('🔍 Search Page: After search filtering:', filtered.length, 'businesses')

      // Then filter by categories if any are selected
      if (selectedCategories.length > 0) {
        filtered = await filterBusinessesByCategories(filtered, selectedCategories)
        console.log('🔍 Search Page: After category filtering:', filtered.length, 'businesses')
      }

      // Apply attribute filters if any are active
      if (activeFilters.length > 0) {
        filtered = filterBusinessesByAttributes(filtered, activeFilters)
        console.log('🔍 Search Page: After attribute filtering:', filtered.length, 'businesses')
      }

      // Apply price range filter (always apply)
      filtered = filtered.filter(business => {
        const passesPrice = business.deliveryFee >= priceRange[0] && business.deliveryFee <= priceRange[1]
        return passesPrice
      })
      console.log('🔍 Search Page: After price filtering:', filtered.length, 'businesses')

      // Apply delivery time filter (always apply)
      filtered = filtered.filter(business => {
        const deliveryTimeNum = typeof business.deliveryTime === 'string' ? parseInt(business.deliveryTime, 10) : business.deliveryTime
        const actualDeliveryTime = deliveryTimeNum || 30
        const passesTime = actualDeliveryTime <= maxDeliveryTime
        return passesTime
      })
      console.log('🔍 Search Page: After delivery time filtering:', filtered.length, 'businesses')

      // Apply prep time filter (always apply)
      filtered = filtered.filter(business => {
        const prepTimeNum = business.preparationTimeMinutes || 15
        const passesPrepTime = prepTimeNum <= maxPrepTime
        return passesPrepTime
      })
      console.log('🔍 Search Page: After prep time filtering:', filtered.length, 'businesses')

      setFilteredBusinesses(filtered)
      console.log('🔍 Search Page: Final filtered businesses:', filtered.length)
    }

    applyFilters()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [businesses, searchQuery, activeFilters, selectedCategories, priceRange, maxDeliveryTime, maxPrepTime])

  // Handle search form submission
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()

    // Update URL with search parameters without page reload
    const url = new URL(window.location.href)
    url.searchParams.set('q', searchQuery)
    url.searchParams.set('type', activeType)
    if (contextPostcode) {
      url.searchParams.set('postcode', contextPostcode)
    }
    window.history.pushState({}, '', url.toString())
  }

  // Handle business type change
  const handleTypeChange = (type: string) => {
    setActiveType(type)
    // Update URL with new type
    const url = new URL(window.location.href)
    url.searchParams.set('type', type)
    window.history.pushState({}, '', url.toString())
  }

  // Get attribute filter functionality
  const { filterBusinessesByAttributes } = useAttributeFilters(activeType)

  // Handle filter changes
  const handleFiltersChange = (filters: string[]) => {
    setActiveFilters(filters)
    // The main filtering logic is now handled in the useEffect above
    // This function just updates the state, which triggers the useEffect
  }

  // Handle price range changes
  const handlePriceRangeChange = (range: [number, number]) => {
    setPriceRange(range)
    // The main filtering logic is now handled in the useEffect above
    // This function just updates the state, which triggers the useEffect
  }

  // Handle delivery time changes
  const handleMaxDeliveryTimeChange = (time: number) => {
    setMaxDeliveryTime(time)
    // The main filtering logic is now handled in the useEffect above
    // This function just updates the state, which triggers the useEffect
  }

  // Handle prep time changes
  const handleMaxPrepTimeChange = (time: number) => {
    setMaxPrepTime(time)
    // The main filtering logic is now handled in the useEffect above
    // This function just updates the state, which triggers the useEffect
  }

  // Helper function to check if a business has attributes matching the search query
  const hasMatchingAttribute = (business: any, query: string): boolean => {
    if (!query) return false

    const lowerQuery = query.toLowerCase()

    if ('cuisines' in business && business.cuisines) {
      return business.cuisines.some((cuisine: string) =>
        cuisine.toLowerCase().includes(lowerQuery)
      )
    }
    if ('storeTypes' in business && business.storeTypes) {
      return business.storeTypes.some((type: string) =>
        type.toLowerCase().includes(lowerQuery)
      )
    }
  }

  // Helper function to filter businesses by selected categories
  const filterBusinessesByCategories = async (businesses: any[], categoryNames: string[]): Promise<any[]> => {
    if (!categoryNames.length) return businesses

    console.log('🔍 filterBusinessesByCategories: Starting with', businesses.length, 'businesses and categories:', categoryNames)

    try {
      // Get category IDs from category names
      const categoryResponse = await fetch(`/api/categories?purpose=specialization`)
      if (!categoryResponse.ok) {
        console.error('🔍 filterBusinessesByCategories: Failed to fetch categories')
        return businesses
      }

      const categoryData = await categoryResponse.json()
      const categories = categoryData.categories || []
      console.log('🔍 filterBusinessesByCategories: Found', categories.length, 'total categories')

      // Find category IDs for the selected category names
      const selectedCategoryIds = categories
        .filter((cat: any) => categoryNames.includes(cat.name))
        .map((cat: any) => cat.id)

      console.log('🔍 filterBusinessesByCategories: Selected category IDs:', selectedCategoryIds)

      if (!selectedCategoryIds.length) {
        console.log('🔍 filterBusinessesByCategories: No matching category IDs found')
        return businesses
      }

      // Get businesses that have selected these categories
      const businessCategoriesResponse = await fetch('/api/business-categories?' + new URLSearchParams({
        categoryIds: selectedCategoryIds.join(',')
      }))

      if (!businessCategoriesResponse.ok) {
        console.error('🔍 filterBusinessesByCategories: Failed to fetch business categories')
        return businesses
      }

      const businessCategoriesData = await businessCategoriesResponse.json()
      const businessIds = businessCategoriesData.businessIds || []
      console.log('🔍 filterBusinessesByCategories: Found business IDs with categories:', businessIds)

      // Filter businesses to only include those with selected categories
      const filteredBusinesses = businesses.filter(business => businessIds.includes(business.id))
      console.log('🔍 filterBusinessesByCategories: Filtered to', filteredBusinesses.length, 'businesses:', filteredBusinesses.map(b => b.name))

      return filteredBusinesses
    } catch (error) {
      console.error('Error filtering businesses by categories:', error)
      return businesses
    }
  }

  return (
    <div className="container-fluid px-2 sm:px-4">
      {/* Postcode Dialog */}
      <Dialog open={postcodeDialogOpen} onOpenChange={setPostcodeDialogOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-md p-4 sm:p-6">
          <DialogTitle className="sr-only">{validationError ? "Invalid Postcode" : "Enter Your Postcode"}</DialogTitle>
          <div className="absolute right-4 top-4">
            <button
              onClick={() => setPostcodeDialogOpen(false)}
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
          {validationError ? (
            <div className="flex flex-col gap-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h3 className="text-lg font-semibold text-red-600">Invalid Postcode</h3>
                </div>
              </div>
              <p className="text-gray-600 mt-1 pl-8">
                {validationError}
              </p>
            </div>
          ) : (
            <DialogHeader>
              <DialogTitle>Enter Your Postcode</DialogTitle>
            </DialogHeader>
          )}
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="postcode">Please enter your postcode to see delivery times and fees</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="postcode"
                  placeholder="e.g. JE2 3NG"
                  className={`pl-10 ${validationError ? 'border-red-300 focus-visible:ring-red-500 focus-visible:border-red-500' : ''}`}
                  value={newPostcode}
                  onChange={(e) => {
                    setNewPostcode(e.target.value);
                    if (validationError) setValidationError(null);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handlePostcodeSubmit();
                    }
                  }}
                />
              </div>
              <p className="text-xs text-gray-500">
                Enter a Jersey postcode (e.g., JE2 3NG) to see delivery times
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Jersey postcodes must be in the format JE1-4 followed by a space, a digit, and two letters
              </p>

              {validationError && (
                <div className="flex items-center justify-between mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setValidationError(null)}
                    className="text-xs"
                  >
                    Try again
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleProceedWithDefault}
                    className="bg-emerald-600 hover:bg-emerald-700 text-xs"
                  >
                    Use default location
                  </Button>
                </div>
              )}
            </div>
          </div>
          {!validationError ? (
            <DialogFooter>
              <Button
                onClick={handlePostcodeSubmit}
                disabled={isSubmitting || !newPostcode.trim()}
                className="bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                {isSubmitting ? "Updating..." : "Continue"}
              </Button>
            </DialogFooter>
          ) : (
            <div className="flex flex-col sm:flex-row gap-2 pt-4 w-full justify-center">
              <Button
                variant="outline"
                onClick={() => setValidationError(null)}
                className="w-[90%] mx-auto sm:w-[45%] text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                size="sm"
                style={{ minHeight: "36px" }}
              >
                Enter a different postcode
              </Button>
              <Button
                onClick={handleProceedWithDefault}
                className="w-[90%] mx-auto sm:w-[55%] bg-emerald-600 hover:bg-emerald-700 text-[11px] sm:text-sm px-1 sm:px-2 whitespace-nowrap"
                size="sm"
                style={{ minHeight: "36px" }}
              >
                Continue with default location
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {isMobile ? (
        // Mobile/Tablet View
        <>
          <MobileSearchPage
            initialQuery={searchQuery}
            initialType={activeType}
            businesses={filteredBusinesses}
            onTypeChange={handleTypeChange}
            onCategorySelect={(category) => {
              console.log('🔍 Mobile Search: Category selected:', category)
              // Toggle category selection
              setSelectedCategories(prev => {
                if (prev.includes(category)) {
                  // Remove category if already selected
                  return prev.filter(cat => cat !== category)
                } else {
                  // Add category if not selected
                  return [...prev, category]
                }
              })
            }}
          />
        </>
      ) : (
        // Desktop View
        <div className="min-h-screen bg-white">
          {/* Categories and Filters */}
          <div className="bg-white border-b border-gray-200">
            <div>
              <CategoriesAndFilters
                onCategorySelect={(category) => {
                  console.log('🔍 Search Page: Category selected:', category)
                  // Toggle category selection
                  setSelectedCategories(prev => {
                    const newCategories = prev.includes(category)
                      ? prev.filter(cat => cat !== category)
                      : [...prev, category]
                    console.log('🔍 Search Page: Updated selected categories:', newCategories)
                    return newCategories
                  })
                }}
                onFilterSelect={(filter) => {
                  // Implement filter logic here
                }}
                resultsCount={!loading ? filteredBusinesses.length : undefined}
                businessType={activeType}
              />
            </div>
          </div>

          {/* Main content container with padding */}
          <div className="py-6">
            {/* Main content with sidebar layout for desktop */}
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Sidebar with sticky positioning */}
              <div className="hidden lg:block lg:w-[280px]">
                <div className="sticky top-[calc(4rem+3rem)] max-h-[calc(100vh-4rem-3rem-2rem)] overflow-y-auto">
                  <div className="bg-white rounded-lg shadow-sm border border-gray-100">
                    <SearchFilters
                      businessType={activeType}
                      onFiltersChange={handleFiltersChange}
                      onPriceRangeChange={handlePriceRangeChange}
                      onMaxDeliveryTimeChange={handleMaxDeliveryTimeChange}
                      onMaxPrepTimeChange={handleMaxPrepTimeChange}
                      onTypeChange={handleTypeChange}
                    />
                  </div>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">


                {/* Search Results */}
                {loading ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {[...Array(8)].map((_, i) => (
                      <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                        <div className="h-48 bg-gray-200"></div>
                        <div className="p-4">
                          <div className="h-5 bg-gray-200 rounded mb-2"></div>
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="flex justify-between">
                            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : filteredBusinesses.length === 0 ? (
                  <div className="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-100">
                    <p className="text-gray-600 mb-4">No results found matching your criteria</p>
                    <Button
                      variant="outline"
                      className="bg-white hover:bg-gray-50 border-[#22c55e] text-[#22c55e]"
                      onClick={() => {
                        // Clear all filter states
                        setSearchQuery('')
                        setActiveType('all')
                        setActiveFilters([])
                        setSelectedCategories([])
                        setPriceRange([0, 50])
                        setMaxDeliveryTime(30)
                        setMaxPrepTime(30)

                        // Also update URL to reflect cleared state
                        const url = new URL(window.location.href)
                        url.searchParams.delete('q')
                        url.searchParams.delete('type')
                        window.history.replaceState({}, '', url.toString())
                      }}
                    >
                      Clear Filters
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 grid-auto-rows-fr">
                    {filteredBusinesses.map((business, index) => {
                      return (
                        <UnifiedBusinessCard
                          key={business.id}
                          business={business as any} // Type assertion for search page compatibility
                          index={index}
                          enableRealTimeUpdates={false} // Search page uses cached values for performance
                        />
                      );
                    })}
                  </div>
                )}

                {/* Add custom animation styles */}
                <style jsx global>{`
                  @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                  }

                  .animate-fadeIn {
                    animation: fadeIn 0.5s ease-out forwards;
                  }

                  .animation-delay-100 { animation-delay: 100ms; }
                  .animation-delay-200 { animation-delay: 200ms; }
                  .animation-delay-300 { animation-delay: 300ms; }
                  .animation-delay-400 { animation-delay: 400ms; }
                  .animation-delay-500 { animation-delay: 500ms; }
                  .animation-delay-600 { animation-delay: 600ms; }
                `}</style>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
